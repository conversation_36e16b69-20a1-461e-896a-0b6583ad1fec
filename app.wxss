/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  margin: 10rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007aff;
  color: white;
}

.btn-primary:active {
  background-color: #0056cc;
  transform: scale(0.95);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-secondary:active {
  background-color: #545b62;
  transform: scale(0.95);
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:active {
  background-color: #1e7e34;
  transform: scale(0.95);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:active {
  background-color: #c82333;
  transform: scale(0.95);
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 文本样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.subtitle {
  font-size: 36rpx;
  font-weight: 500;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #007aff;
}

.text-success {
  color: #28a745;
}

.text-danger {
  color: #dc3545;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

/* 间距样式 */
.mt-20 {
  margin-top: 20rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-40 {
  padding: 40rpx;
}
