/* app.wxss - 全局样式 */

/* 页面基础样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
}

/* 容器样式 */
.container {
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: 24rpx 48rpx;
  margin: 12rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: #4CAF50;
  color: white;
}

.btn-primary:active {
  background-color: #45a049;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:active {
  background-color: #5a6268;
  transform: scale(0.98);
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:active {
  background-color: #218838;
  transform: scale(0.98);
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:active {
  background-color: #e0a800;
  transform: scale(0.98);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:active {
  background-color: #c82333;
  transform: scale(0.98);
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 16rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 文本样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
}

.subtitle {
  font-size: 36rpx;
  font-weight: 500;
  color: #666;
  text-align: center;
  margin-bottom: 24rpx;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:active {
  background-color: #545b62;
  transform: scale(0.95);
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:active {
  background-color: #1e7e34;
  transform: scale(0.95);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:active {
  background-color: #c82333;
  transform: scale(0.95);
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:active {
  background-color: #e0a800;
  transform: scale(0.95);
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 文本样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.subtitle {
  font-size: 36rpx;
  font-weight: 500;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #007aff;
}

.text-success {
  color: #28a745;
}

.text-danger {
  color: #dc3545;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

/* 间距样式 */
.mt-20 {
  margin-top: 20rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-40 {
  padding: 40rpx;
}
