// pages/settings/settings.js
Page({
  data: {
    gameSettings: {
      difficulty: 'normal',
      soundEnabled: true,
      vibrationEnabled: true
    }
  },

  onLoad: function (options) {
    const app = getApp();
    this.setData({
      gameSettings: app.globalData.gameSettings
    });
  },

  // 切换难度
  onDifficultyChange: function(e) {
    const difficulty = e.detail.value;
    const difficultyMap = ['easy', 'normal', 'hard'];
    const selectedDifficulty = difficultyMap[difficulty];
    
    this.setData({
      'gameSettings.difficulty': selectedDifficulty
    });
    
    this.saveSettings();
  },

  // 切换音效
  onSoundToggle: function(e) {
    this.setData({
      'gameSettings.soundEnabled': e.detail.value
    });
    this.saveSettings();
  },

  // 切换震动
  onVibrationToggle: function(e) {
    this.setData({
      'gameSettings.vibrationEnabled': e.detail.value
    });
    this.saveSettings();
  },

  // 保存设置
  saveSettings: function() {
    const app = getApp();
    app.globalData.gameSettings = this.data.gameSettings;
    
    wx.setStorageSync('gameSettings', this.data.gameSettings);
    
    wx.showToast({
      title: '设置已保存',
      icon: 'success',
      duration: 1000
    });
  },

  // 清除游戏数据
  clearGameData: function() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有游戏数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync();
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 关于游戏
  showAbout: function() {
    wx.showModal({
      title: '关于游戏',
      content: '背单词切字母 v1.0\n\n一款有趣的英语单词学习游戏，通过拼写单词的方式帮助用户学习和记忆英语单词。\n\n开发者：您的名字',
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
