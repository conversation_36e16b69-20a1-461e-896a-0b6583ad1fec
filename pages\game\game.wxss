/* pages/game/game.wxss */
.game-container {
  padding: 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

/* 成功提示横幅 */
.success-banner {
  background: #2196F3;
  color: white;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.3);
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 游戏信息栏 */
.game-info-bar {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  text-align: center;
  flex: 1;
}

.info-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
}

/* 单词显示区域 */
.word-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.word-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.word-display {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.letter-slot {
  width: 60rpx;
  height: 60rpx;
  border: 3rpx solid #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  background: white;
  color: #999;
}

.letter-slot.filled {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
  transform: scale(1.1);
}

.word-hint {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
}

/* 键盘区域 */
.keyboard-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.keyboard-title {
  display: block;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.keyboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12rpx;
}

.letter-btn {
  width: 80rpx;
  height: 80rpx;
  background: #2196F3;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
}

.letter-btn:active {
  transform: scale(0.95);
  background: #1976D2;
}

/* 游戏控制 */
.game-controls {
  text-align: center;
  margin-bottom: 30rpx;
}

.control-btn {
  margin: 8rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}

.playing-controls {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

/* 调试信息 */
.debug-section {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.debug-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.debug-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.debug-btn {
  background: none;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  margin-top: 16rpx;
}

/* 返回按钮 */
.back-section {
  text-align: center;
  padding-bottom: 40rpx;
}

.back-btn {
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}