/* pages/game/game.wxss */
.game-container {
  padding: 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 游戏头部信息 */
.game-header {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 单词显示区域 */
.word-display-area {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.word-display {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.letter-slot {
  width: 60rpx;
  height: 60rpx;
  border: 3rpx solid #ddd;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  background: white;
  transition: all 0.3s ease;
}

.letter-slot.filled {
  background: #28a745;
  color: white;
  border-color: #28a745;
  transform: scale(1.1);
}

.word-hint {
  padding: 20rpx;
  background: #fff3cd;
  border-radius: 10rpx;
  border: 1rpx solid #ffeaa7;
}

.word-hint text {
  color: #856404;
  font-size: 28rpx;
}

/* 键盘区域 */
.keyboard-area {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.keyboard-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.keyboard {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15rpx;
}

.letter-key {
  width: 80rpx;
  height: 80rpx;
  background: #007aff;
  color: white;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.letter-key:active {
  transform: scale(0.9);
  background: #0056cc;
}

/* 游戏控制按钮 */
.game-controls {
  margin-bottom: 30rpx;
  text-align: center;
}

.start-btn {
  width: 300rpx;
  height: 80rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.playing-controls {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
}

.playing-controls .btn {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

/* 游戏状态提示 */
.game-status {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.debug-info {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  padding: 10rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
}

/* 已完成单词列表 */
.completed-words {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.completed-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.completed-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: center;
}

.completed-word {
  background: #28a745;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .letter-key {
    width: 70rpx;
    height: 70rpx;
    font-size: 28rpx;
  }
  
  .letter-slot {
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }
}
