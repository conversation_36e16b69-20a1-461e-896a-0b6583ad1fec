<!--pages/result/result.wxml-->
<view class="result-container">
  <view class="result-header">
    <text class="header-title">游戏记录</text>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stats-grid">
      <view class="stat-card">
        <text class="stat-number">{{totalGames}}</text>
        <text class="stat-label">总游戏次数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{bestScore}}</text>
        <text class="stat-label">最高分数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{totalWords}}</text>
        <text class="stat-label">完成单词数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{averageScore}}</text>
        <text class="stat-label">平均分数</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn btn-primary" bindtap="startNewGame">开始新游戏</button>
    <button class="btn btn-secondary" bindtap="shareScore">分享成绩</button>
    <button class="btn btn-danger" bindtap="clearHistory">清除记录</button>
  </view>

  <!-- 游戏历史列表 -->
  <view class="history-section" wx:if="{{gameHistory.length > 0}}">
    <view class="section-title">最近游戏记录</view>
    <view class="history-list">
      <view 
        wx:for="{{gameHistory}}" 
        wx:key="index"
        class="history-item"
        data-index="{{index}}"
        bindtap="viewGameDetail"
      >
        <view class="history-main">
          <view class="history-score">{{item.score}}分</view>
          <view class="history-info">
            <text class="history-words">{{item.wordsCompleted}}个单词</text>
            <text class="history-level">Lv.{{item.maxLevel}}</text>
          </view>
        </view>
        <view class="history-meta">
          <text class="history-date">{{item.date}}</text>
          <text class="history-duration">{{item.duration}}秒</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{gameHistory.length === 0}}">
    <text class="empty-icon">🎮</text>
    <text class="empty-title">还没有游戏记录</text>
    <text class="empty-desc">开始你的第一场游戏吧！</text>
    <button class="btn btn-primary empty-btn" bindtap="startNewGame">
      开始游戏
    </button>
  </view>
</view>
