// pages/game/game.js - 完全简化版本
Page({
  data: {
    currentWord: '',
    currentWordDisplay: [],
    userInput: '',
    availableLetters: [],
    score: 0,
    level: 1,
    timeLeft: 60,
    gameStatus: 'ready',
    wordList: [],
    completedWords: [],
    hints: 3,
    showHint: false,
    hintText: ''
  },

  onLoad: function (options) {
    console.log('🎮 游戏页面开始加载');
    
    // 使用简单的默认单词库
    const defaultWordList = [
      'CAT', 'DOG', 'SUN', 'MOON', 'STAR',
      'BOOK', 'TREE', 'FISH', 'BIRD', 'LOVE'
    ];
    
    this.setData({
      wordList: defaultWordList
    });
    
    console.log('✅ 单词库设置完成:', this.data.wordList);
    
    // 简化初始化
    this.simpleInit();
  },

  onReady: function() {
    console.log('✅ 游戏页面渲染完成');
  },

  // 简化的初始化
  simpleInit: function() {
    console.log('🔄 开始简化初始化');
    
    // 选择第一个单词
    const firstWord = this.data.wordList[0] || 'TEST';
    const wordDisplay = firstWord.split('').map(() => '_');
    
    // 生成简单的字母数组
    const letters = firstWord.split('');
    const extraLetters = ['X', 'Y', 'Z', 'Q', 'W'];
    const allLetters = [...letters, ...extraLetters].slice(0, 8);
    
    this.setData({
      currentWord: firstWord,
      currentWordDisplay: wordDisplay,
      availableLetters: this.shuffleArray(allLetters),
      gameStatus: 'ready',
      userInput: ''
    });
    
    console.log('✅ 初始化完成');
    console.log('当前单词:', firstWord);
    console.log('可用字母:', allLetters);
  },

  // 打乱数组
  shuffleArray: function(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  },

  // 开始游戏
  startGame: function() {
    console.log('🎮 开始游戏');
    this.setData({
      gameStatus: 'playing'
    });
  },

  // 点击字母
  onLetterTap: function(e) {
    const { letter, index } = e.currentTarget.dataset;
    const { currentWord, currentWordDisplay, userInput, gameStatus } = this.data;
    
    console.log('点击字母:', letter, '当前输入:', userInput);
    
    // 自动开始游戏
    if (gameStatus === 'ready') {
      this.startGame();
    }
    
    // 检查字母是否正确
    if (currentWord[userInput.length] === letter) {
      console.log('✅ 字母正确!');
      
      // 更新显示
      const newWordDisplay = [...currentWordDisplay];
      newWordDisplay[userInput.length] = letter;
      const newUserInput = userInput + letter;
      
      this.setData({
        currentWordDisplay: newWordDisplay,
        userInput: newUserInput
      });
      
      // 检查是否完成
      if (newUserInput === currentWord) {
        console.log('🎉 单词完成!');
        wx.showToast({
          title: '完成！',
          icon: 'success'
        });
      }
    } else {
      console.log('❌ 字母错误');
      wx.showToast({
        title: '字母错误',
        icon: 'error'
      });
    }
  },

  // 重新初始化
  initGame: function() {
    console.log('🔄 重新初始化游戏');
    this.simpleInit();
  },

  // 返回首页
  goBack: function() {
    console.log('🏠 返回首页');
    wx.navigateBack({
      fail: function() {
        wx.redirectTo({
          url: '/pages/index/index'
        });
      }
    });
  },

  // 显示调试信息
  testGame: function() {
    console.log('=== 🔧 调试信息 ===');
    console.log('当前单词:', this.data.currentWord);
    console.log('单词显示:', this.data.currentWordDisplay);
    console.log('用户输入:', this.data.userInput);
    console.log('可用字母:', this.data.availableLetters);
    console.log('游戏状态:', this.data.gameStatus);
    console.log('单词库:', this.data.wordList);
    console.log('==================');

    wx.showModal({
      title: '调试信息',
      content: `单词: ${this.data.currentWord}\n状态: ${this.data.gameStatus}\n输入: ${this.data.userInput}`,
      showCancel: false
    });
  },

  // 重置游戏
  resetGame: function() {
    console.log('🔄 重置游戏');
    this.simpleInit();
  },

  // 暂停游戏
  pauseGame: function() {
    console.log('⏸️ 暂停游戏');
    this.setData({
      gameStatus: 'paused'
    });
  },

  // 继续游戏
  resumeGame: function() {
    console.log('▶️ 继续游戏');
    this.setData({
      gameStatus: 'playing'
    });
  },

  // 显示详细调试信息
  showDebugInfo: function() {
    const debugInfo = {
      currentWord: this.data.currentWord,
      currentWordDisplay: this.data.currentWordDisplay,
      userInput: this.data.userInput,
      availableLetters: this.data.availableLetters,
      gameStatus: this.data.gameStatus,
      wordListLength: this.data.wordList.length
    };

    console.log('📋 详细调试信息:', debugInfo);

    wx.showModal({
      title: '详细调试信息',
      content: JSON.stringify(debugInfo, null, 2),
      showCancel: false
    });
  }
});
