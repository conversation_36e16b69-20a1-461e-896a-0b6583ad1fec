// pages/game/game.js
Page({
  data: {
    currentWord: '',
    currentWordDisplay: [],
    userInput: '',
    availableLetters: [],
    score: 0,
    level: 1,
    timeLeft: 60,
    gameStatus: 'ready', // ready, playing, paused, finished
    wordList: [],
    completedWords: [],
    hints: 3,
    showHint: false,
    hintText: ''
  },

  onLoad: function (options) {
    // 获取全局单词库
    const app = getApp();
    this.setData({
      wordList: app.globalData.wordList
    });
    this.initGame();
  },

  // 初始化游戏
  initGame: function() {
    this.generateNewWord();
    this.generateAvailableLetters();
    this.setData({
      score: 0,
      level: 1,
      timeLeft: 60,
      gameStatus: 'ready',
      completedWords: [],
      hints: 3,
      userInput: ''
    });
  },

  // 生成新单词
  generateNewWord: function() {
    const { wordList, completedWords } = this.data;
    const availableWords = wordList.filter(word => !completedWords.includes(word));
    
    if (availableWords.length === 0) {
      // 所有单词都完成了，重置已完成列表
      this.setData({
        completedWords: []
      });
      return this.generateNewWord();
    }

    const randomIndex = Math.floor(Math.random() * availableWords.length);
    const newWord = availableWords[randomIndex];
    
    // 创建单词显示数组，用下划线表示未填入的字母
    const wordDisplay = newWord.split('').map(() => '_');
    
    this.setData({
      currentWord: newWord,
      currentWordDisplay: wordDisplay,
      userInput: ''
    });
  },

  // 生成可用字母（包含目标单词的字母和一些干扰字母）
  generateAvailableLetters: function() {
    const { currentWord } = this.data;
    const targetLetters = currentWord.split('');
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    
    // 确保目标单词的所有字母都在可用字母中
    let availableLetters = [...targetLetters];
    
    // 添加一些随机干扰字母，总共15个字母
    while (availableLetters.length < 15) {
      const randomLetter = allLetters[Math.floor(Math.random() * allLetters.length)];
      availableLetters.push(randomLetter);
    }
    
    // 打乱字母顺序
    availableLetters = this.shuffleArray(availableLetters);
    
    this.setData({
      availableLetters: availableLetters
    });
  },

  // 打乱数组
  shuffleArray: function(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  },

  // 点击字母 - 这是关键的字母替换逻辑
  onLetterTap: function(e) {
    const { letter, index } = e.currentTarget.dataset;
    const { currentWord, currentWordDisplay, userInput, gameStatus } = this.data;

    console.log('点击字母:', letter, '索引:', index, '当前单词:', currentWord, '用户输入:', userInput);

    if (gameStatus !== 'playing') return;

    // 检查这个字母是否在目标单词中的正确位置
    if (currentWord[userInput.length] === letter) {
      console.log('字母正确！开始替换...');

      // 字母正确，更新显示
      const newWordDisplay = [...currentWordDisplay];
      newWordDisplay[userInput.length] = letter;
      const newUserInput = userInput + letter;

      // 关键：生成新字母替换被使用的字母
      const newAvailableLetters = [...this.data.availableLetters];
      const newLetter = this.generateNewLetter(newUserInput);
      newAvailableLetters[index] = newLetter;

      console.log('原字母:', letter, '新字母:', newLetter, '位置:', index);
      console.log('更新前的字母数组:', this.data.availableLetters);
      console.log('更新后的字母数组:', newAvailableLetters);

      this.setData({
        currentWordDisplay: newWordDisplay,
        userInput: newUserInput,
        availableLetters: newAvailableLetters
      });

      // 检查是否完成单词
      if (newUserInput === currentWord) {
        this.onWordCompleted();
      }

      // 播放成功音效
      this.playSound('success');
    } else {
      console.log('字母错误:', letter, '期望:', currentWord[userInput.length]);
      // 字母错误，播放错误音效
      this.playSound('error');
      wx.vibrateShort();
    }
  },

  // 生成新字母（用于替换被使用的字母）
  generateNewLetter: function(currentUserInput) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

    // 简化逻辑：直接返回随机字母，确保替换功能先能工作
    const newLetter = allLetters[Math.floor(Math.random() * allLetters.length)];
    console.log('生成新字母:', newLetter);

    return newLetter;
  },

  // 单词完成
  onWordCompleted: function() {
    const { currentWord, completedWords, score, level } = this.data;
    const newCompletedWords = [...completedWords, currentWord];
    const newScore = score + currentWord.length * 10 * level;
    
    this.setData({
      completedWords: newCompletedWords,
      score: newScore
    });
    
    // 显示完成提示
    wx.showToast({
      title: `完成！+${currentWord.length * 10 * level}分`,
      icon: 'success',
      duration: 1500
    });
    
    // 延迟生成下一个单词
    setTimeout(() => {
      this.generateNewWord();
      this.generateAvailableLetters();
      
      // 检查是否升级
      if (newCompletedWords.length % 5 === 0) {
        this.levelUp();
      }
    }, 1500);
  },

  // 升级
  levelUp: function() {
    const newLevel = this.data.level + 1;
    this.setData({
      level: newLevel,
      timeLeft: this.data.timeLeft + 30 // 每升一级增加30秒
    });
    
    wx.showModal({
      title: '恭喜升级！',
      content: `升级到第${newLevel}级！获得30秒额外时间！`,
      showCancel: false
    });
  },

  // 开始游戏
  startGame: function() {
    this.setData({
      gameStatus: 'playing'
    });
    this.startTimer();
  },

  // 开始计时器
  startTimer: function() {
    this.timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({
        timeLeft: timeLeft
      });
      
      if (timeLeft <= 0) {
        this.gameOver();
      }
    }, 1000);
  },

  // 游戏结束
  gameOver: function() {
    clearInterval(this.timer);
    this.setData({
      gameStatus: 'finished'
    });

    // 保存游戏记录
    this.saveGameRecord();

    wx.showModal({
      title: '游戏结束',
      content: `最终得分：${this.data.score}分\n完成单词：${this.data.completedWords.length}个`,
      confirmText: '重新开始',
      cancelText: '返回首页',
      success: (res) => {
        if (res.confirm) {
          this.initGame();
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 保存游戏记录
  saveGameRecord: function() {
    const { score, completedWords, level } = this.data;
    const gameRecord = {
      score: score,
      wordsCompleted: completedWords.length,
      maxLevel: level,
      date: new Date().toLocaleString(),
      duration: 60 - this.data.timeLeft, // 游戏时长
      completedWordsList: completedWords
    };

    // 获取现有记录
    let gameHistory = wx.getStorageSync('gameHistory') || [];
    gameHistory.unshift(gameRecord); // 添加到开头

    // 只保留最近100条记录
    if (gameHistory.length > 100) {
      gameHistory = gameHistory.slice(0, 100);
    }

    // 保存记录
    wx.setStorageSync('gameHistory', gameHistory);

    // 更新统计数据
    this.updateGameStats(gameRecord);
  },

  // 更新游戏统计
  updateGameStats: function(gameRecord) {
    let stats = wx.getStorageSync('gameStats') || {
      totalGames: 0,
      bestScore: 0,
      totalWords: 0
    };

    stats.totalGames += 1;
    stats.bestScore = Math.max(stats.bestScore, gameRecord.score);
    stats.totalWords += gameRecord.wordsCompleted;

    wx.setStorageSync('gameStats', stats);

    // 更新全局数据
    const app = getApp();
    if (app.globalData) {
      app.globalData.gameStats = stats;
    }
  },

  // 播放音效
  playSound: function(type) {
    const app = getApp();
    if (app.globalData.gameSettings.soundEnabled) {
      // 这里可以添加音效播放逻辑
      console.log(`播放${type}音效`);
    }
  },

  // 暂停游戏
  pauseGame: function() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.setData({
      gameStatus: 'paused'
    });
  },

  // 继续游戏
  resumeGame: function() {
    this.setData({
      gameStatus: 'playing'
    });
    this.startTimer();
  },

  // 使用提示
  useHint: function() {
    const { hints, currentWord, userInput } = this.data;

    if (hints <= 0) {
      wx.showToast({
        title: '没有提示了',
        icon: 'none'
      });
      return;
    }

    // 显示下一个字母的提示
    const nextLetter = currentWord[userInput.length];
    this.setData({
      hints: hints - 1,
      showHint: true,
      hintText: `下一个字母是: ${nextLetter}`
    });

    // 3秒后隐藏提示
    setTimeout(() => {
      this.setData({
        showHint: false,
        hintText: ''
      });
    }, 3000);
  },

  // 跳过单词
  skipWord: function() {
    wx.showModal({
      title: '确认跳过',
      content: '跳过当前单词将不会获得分数，确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          this.generateNewWord();
          this.generateAvailableLetters();
        }
      }
    });
  },

  // 页面卸载时清理定时器
  onUnload: function() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
});
