/* pages/settings/settings.wxss */
.settings-container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.settings-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.setting-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.picker-label {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.picker-value {
  font-size: 32rpx;
  color: #007aff;
  margin-right: 20rpx;
}

.picker-arrow {
  font-size: 32rpx;
  color: #999;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.switch-label {
  font-size: 32rpx;
  color: #333;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
  line-height: 1.5;
}

.clear-btn, .about-btn {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  border-radius: 15rpx;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
}

.clear-btn:active {
  background: #c82333;
}

.about-btn {
  background: #6c757d;
  color: white;
  border: none;
}

.about-btn:active {
  background: #545b62;
}
