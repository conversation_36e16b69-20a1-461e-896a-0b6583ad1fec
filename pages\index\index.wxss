/* pages/index/index.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 游戏标题 */
.game-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-main {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  margin-bottom: 20rpx;
}

.title-sub {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* 用户信息区域 */
.user-info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #007aff;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.get-user-btn {
  margin-top: 20rpx;
  width: 300rpx;
  height: 70rpx;
  font-size: 28rpx;
}

/* 游戏统计 */
.game-stats {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 主要操作按钮 */
.main-actions {
  margin-bottom: 40rpx;
}

.start-game-btn {
  width: 100%;
  height: 120rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  color: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(40, 167, 69, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-game-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 36rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-btn {
  height: 100rpx;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 15rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 2rpx solid #ddd;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.7);
}

.action-btn .btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.action-btn .btn-text {
  font-size: 24rpx;
}

/* 游戏特色介绍 */
.game-features {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.features-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 15rpx;
  border-left: 6rpx solid #007aff;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .title-main {
    font-size: 56rpx;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    flex-direction: column;
    gap: 20rpx;
  }
}
