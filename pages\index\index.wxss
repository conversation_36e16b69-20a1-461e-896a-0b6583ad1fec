/* pages/index/index.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

/* 应用标题 */
.app-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 40rpx 0;
}

.app-title {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  margin-bottom: 16rpx;
}

.app-subtitle {
  display: block;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  margin-bottom: 12rpx;
}

.app-version {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 快速开始 */
.quick-start {
  text-align: center;
  margin-bottom: 40rpx;
}

.start-game-btn {
  width: 100%;
  height: 120rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  border: none;
  color: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
}

.start-game-btn:active {
  transform: scale(0.98);
}

.start-hint {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16rpx;
}

/* 游戏统计 */
.game-stats {
  margin-bottom: 40rpx;
}

.stats-title {
  display: block;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.function-buttons .btn {
  flex: 1;
  min-width: 200rpx;
  font-size: 28rpx;
}

/* 调试信息 */
.debug-info {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 40rpx;
}

.debug-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.debug-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 40rpx 0;
}

.footer-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
}

.btn-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .app-title {
    font-size: 56rpx;
  }
  
  .function-buttons {
    flex-direction: column;
  }
  
  .stats-grid {
    flex-direction: column;
    gap: 20rpx;
  }
}
