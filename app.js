// app.js - 背单词切字母小程序
App({
  onLaunch() {
    console.log('🚀 应用启动');

    // 初始化本地存储
    const logs = wx.getStorageSync('logs') || [];
    logs.unshift(Date.now());
    wx.setStorageSync('logs', logs);

    // 初始化游戏设置
    this.initGameSettings();

    console.log('✅ 应用初始化完成');
  },

  onShow() {
    console.log('📱 应用显示');
  },

  onHide() {
    console.log('📱 应用隐藏');
  },

  // 初始化游戏设置
  initGameSettings() {
    const savedSettings = wx.getStorageSync('gameSettings');
    if (!savedSettings) {
      const defaultSettings = {
        difficulty: 'normal',
        soundEnabled: true,
        vibrationEnabled: true
      };
      wx.setStorageSync('gameSettings', defaultSettings);
      this.globalData.gameSettings = defaultSettings;
    } else {
      this.globalData.gameSettings = savedSettings;
    }
  },

  globalData: {
    userInfo: null,
    // 游戏设置
    gameSettings: {
      difficulty: 'normal',
      soundEnabled: true,
      vibrationEnabled: true
    },
    // 简单的单词库
    wordList: [
      'CAT', 'DOG', 'SUN', 'MOON', 'STAR',
      'BOOK', 'TREE', 'FISH', 'BIRD', 'LOVE',
      'APPLE', 'WATER', 'HAPPY', 'SMILE', 'HOUSE'
    ],
    // 游戏统计
    gameStats: {
      totalGames: 0,
      bestScore: 0,
      totalWords: 0
    }
  }
});
