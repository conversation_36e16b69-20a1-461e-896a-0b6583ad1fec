// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    // 游戏相关全局数据
    gameSettings: {
      difficulty: 'normal', // easy, normal, hard
      soundEnabled: true,
      vibrationEnabled: true
    },
    // 单词库
    wordList: [
      'APPLE', 'BANANA', 'ORANGE', 'GRAPE', 'LEMON',
      'WATER', 'BREAD', 'MUSIC', 'HAPPY', 'SMILE',
      'HOUSE', 'PHONE', 'COMPUTER', 'SCHOOL', 'FRIEND',
      'FAMILY', 'LOVE', 'PEACE', 'DREAM', 'HOPE'
    ]
  }
})
