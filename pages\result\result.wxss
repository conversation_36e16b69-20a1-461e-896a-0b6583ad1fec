/* pages/result/result.wxss */
.result-container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.result-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 统计概览 */
.stats-overview {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 15rpx;
  border: 2rpx solid rgba(0, 122, 255, 0.2);
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-buttons .btn {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  border-radius: 15rpx;
}

/* 游戏历史列表 */
.history-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 15rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.history-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.history-main {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.history-score {
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.history-words {
  font-size: 28rpx;
  color: #333;
}

.history-level {
  font-size: 24rpx;
  color: #666;
}

.history-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5rpx;
}

.history-date {
  font-size: 24rpx;
  color: #666;
}

.history-duration {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  text-align: center;
}

.empty-btn {
  width: 300rpx;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
  }
  
  .history-meta {
    align-items: flex-start;
  }
}
