// pages/result/result.js
Page({
  data: {
    gameHistory: [],
    totalGames: 0,
    bestScore: 0,
    totalWords: 0,
    averageScore: 0
  },

  onLoad: function (options) {
    this.loadGameHistory();
  },

  onShow: function() {
    this.loadGameHistory();
  },

  // 加载游戏历史
  loadGameHistory: function() {
    const history = wx.getStorageSync('gameHistory') || [];
    const stats = this.calculateStats(history);
    
    this.setData({
      gameHistory: history.slice(0, 20), // 只显示最近20次游戏
      ...stats
    });
  },

  // 计算统计数据
  calculateStats: function(history) {
    if (history.length === 0) {
      return {
        totalGames: 0,
        bestScore: 0,
        totalWords: 0,
        averageScore: 0
      };
    }

    const totalGames = history.length;
    const bestScore = Math.max(...history.map(game => game.score));
    const totalWords = history.reduce((sum, game) => sum + game.wordsCompleted, 0);
    const totalScore = history.reduce((sum, game) => sum + game.score, 0);
    const averageScore = Math.round(totalScore / totalGames);

    return {
      totalGames,
      bestScore,
      totalWords,
      averageScore
    };
  },

  // 清除游戏历史
  clearHistory: function() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有游戏记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('gameHistory');
          this.setData({
            gameHistory: [],
            totalGames: 0,
            bestScore: 0,
            totalWords: 0,
            averageScore: 0
          });
          
          wx.showToast({
            title: '记录已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看游戏详情
  viewGameDetail: function(e) {
    const index = e.currentTarget.dataset.index;
    const game = this.data.gameHistory[index];
    
    wx.showModal({
      title: '游戏详情',
      content: `游戏时间：${game.date}\n最终得分：${game.score}分\n完成单词：${game.wordsCompleted}个\n游戏时长：${game.duration}秒\n最高等级：${game.maxLevel}级`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 分享成绩
  shareScore: function() {
    const { bestScore, totalWords, totalGames } = this.data;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 开始新游戏
  startNewGame: function() {
    wx.navigateTo({
      url: '/pages/game/game'
    });
  }
});
