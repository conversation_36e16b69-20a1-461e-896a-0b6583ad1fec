// pages/index/index.js
Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'),
    gameStats: {
      totalGames: 0,
      bestScore: 0,
      totalWords: 0
    }
  },

  onLoad: function () {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    this.loadGameStats();
  },

  // 加载游戏统计数据
  loadGameStats: function() {
    const stats = wx.getStorageSync('gameStats') || {
      totalGames: 0,
      bestScore: 0,
      totalWords: 0
    };
    this.setData({
      gameStats: stats
    });
  },

  // 获取用户信息
  getUserProfile: function(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  // 获取用户信息（旧版本兼容）
  getUserInfo: function(e) {
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },

  // 开始游戏
  startGame: function() {
    wx.navigateTo({
      url: '/pages/game/game'
    });
  },

  // 查看设置
  openSettings: function() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 查看游戏记录
  viewGameHistory: function() {
    wx.navigateTo({
      url: '/pages/result/result'
    });
  },

  // 查看游戏规则
  showGameRules: function() {
    wx.showModal({
      title: '游戏规则',
      content: '1. 根据提示拼写英文单词\n2. 点击键盘上的字母进行拼写\n3. 正确拼写获得分数\n4. 完成更多单词可以升级\n5. 每级有时间限制，合理使用提示',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 分享游戏
  onShareAppMessage: function() {
    return {
      title: '背单词切字母 - 有趣的英语学习游戏',
      path: '/pages/index/index',
      imageUrl: '/images/share.png' // 如果有分享图片的话
    }
  }
});
