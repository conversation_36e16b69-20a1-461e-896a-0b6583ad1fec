背单词切字母 - 微信小程序启动说明
================================

如果遇到"模拟器启动失败"或"app.json is not found"错误，请按以下步骤操作：

方法1：重新导入项目（推荐）
--------------------------
1. 完全关闭微信开发者工具
2. 重新打开微信开发者工具
3. 点击左上角"+"号，选择"导入项目"
4. 在目录选择中，选择以下路径：
   C:\Users\<USER>\Desktop\6656AA
5. AppID填写：touristappid 或选择"使用测试号"
6. 项目名称：背单词切字母
7. 点击"导入"

方法2：新建项目
--------------
如果导入失败，可以：
1. 在微信开发者工具中新建一个空白小程序项目
2. 将6656AA文件夹中的所有文件复制到新项目目录
3. 重启开发者工具

注意事项：
---------
- 确保选择的是6656AA文件夹本身，不是其父目录
- 如果提示AppID无效，请使用"测试号"
- 项目使用了Canvas组件，请确保开发者工具版本较新

项目文件结构：
-------------
6656AA/
├── app.json              # 小程序配置文件
├── app.js                # 小程序入口文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
└── pages/                # 页面文件夹
    ├── index/            # 首页
    ├── game/             # 游戏页面
    ├── settings/         # 设置页面
    └── result/           # 结果页面 