<!--pages/settings/settings.wxml-->
<view class="settings-container">
  <view class="settings-header">
    <text class="header-title">游戏设置</text>
  </view>

  <!-- 游戏难度设置 -->
  <view class="setting-section">
    <view class="section-title">游戏难度</view>
    <picker 
      mode="selector" 
      range="{{['简单', '普通', '困难']}}" 
      value="{{gameSettings.difficulty === 'easy' ? 0 : gameSettings.difficulty === 'normal' ? 1 : 2}}"
      bindchange="onDifficultyChange"
    >
      <view class="picker-item">
        <text class="picker-label">当前难度</text>
        <text class="picker-value">
          {{gameSettings.difficulty === 'easy' ? '简单' : gameSettings.difficulty === 'normal' ? '普通' : '困难'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </picker>
    <view class="setting-desc">
      简单：更多提示，更长时间；困难：更少提示，更短时间
    </view>
  </view>

  <!-- 音效设置 -->
  <view class="setting-section">
    <view class="section-title">音效设置</view>
    <view class="switch-item">
      <text class="switch-label">游戏音效</text>
      <switch 
        checked="{{gameSettings.soundEnabled}}" 
        bindchange="onSoundToggle"
        color="#007aff"
      />
    </view>
    <view class="setting-desc">
      开启后游戏中会播放音效提示
    </view>
  </view>

  <!-- 震动设置 -->
  <view class="setting-section">
    <view class="section-title">震动设置</view>
    <view class="switch-item">
      <text class="switch-label">震动反馈</text>
      <switch 
        checked="{{gameSettings.vibrationEnabled}}" 
        bindchange="onVibrationToggle"
        color="#007aff"
      />
    </view>
    <view class="setting-desc">
      开启后错误操作时会有震动提示
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="setting-section">
    <view class="section-title">数据管理</view>
    <button class="btn btn-danger clear-btn" bindtap="clearGameData">
      清除游戏数据
    </button>
    <view class="setting-desc">
      清除所有游戏记录和统计数据
    </view>
  </view>

  <!-- 关于游戏 -->
  <view class="setting-section">
    <view class="section-title">关于</view>
    <button class="btn btn-secondary about-btn" bindtap="showAbout">
      关于游戏
    </button>
  </view>
</view>
