// pages/index/index.js
Page({
  data: {
    gameStats: {
      totalGames: 0,
      bestScore: 0,
      totalWords: 0
    },
    wordListCount: 0,
    appVersion: 'v2.0',
    showDebug: false
  },

  onLoad: function() {
    console.log('🏠 首页加载');
    this.loadGameStats();
    this.loadAppInfo();
  },

  onShow: function() {
    console.log('🏠 首页显示');
    this.loadGameStats();
  },

  // 加载游戏统计
  loadGameStats: function() {
    try {
      const stats = wx.getStorageSync('gameStats') || {
        totalGames: 0,
        bestScore: 0,
        totalWords: 0
      };
      
      this.setData({
        gameStats: stats
      });
      
      console.log('📊 游戏统计加载完成:', stats);
    } catch (error) {
      console.error('❌ 加载游戏统计失败:', error);
    }
  },

  // 加载应用信息
  loadAppInfo: function() {
    const app = getApp();
    const wordListCount = app.globalData.wordList ? app.globalData.wordList.length : 0;
    
    this.setData({
      wordListCount: wordListCount
    });
    
    console.log('📱 应用信息加载完成, 单词库数量:', wordListCount);
  },

  // 开始游戏
  startGame: function() {
    console.log('🎮 点击开始游戏');
    
    wx.showLoading({
      title: '正在进入游戏...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      
      wx.navigateTo({
        url: '/pages/game/game',
        success: function() {
          console.log('✅ 成功跳转到游戏页面');
        },
        fail: function(error) {
          console.error('❌ 跳转游戏页面失败:', error);
          wx.showToast({
            title: '跳转失败，请重试',
            icon: 'error'
          });
        }
      });
    }, 500);
  },

  // 打开设置
  openSettings: function() {
    console.log('⚙️ 打开设置');
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 查看游戏记录
  viewGameHistory: function() {
    console.log('📈 查看游戏记录');
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 显示游戏规则
  showGameRules: function() {
    console.log('📖 显示游戏规则');
    wx.showModal({
      title: '🎮 游戏规则',
      content: '1. 根据提示单词选择正确的字母\n2. 按顺序拼写完整单词\n3. 完成更多单词获得高分\n4. 挑战自己的最佳记录！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 切换调试信息显示
  toggleDebug: function() {
    const showDebug = !this.data.showDebug;
    this.setData({
      showDebug: showDebug
    });
    console.log('🔧 调试信息显示:', showDebug ? '开启' : '关闭');
  }
});
