<!--pages/game/game.wxml-->
<view class="game-container">
  <!-- 游戏头部信息 -->
  <view class="game-header">
    <view class="game-info">
      <view class="info-item">
        <text class="info-label">得分</text>
        <text class="info-value">{{score}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">等级</text>
        <text class="info-value">{{level}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">时间</text>
        <text class="info-value">{{timeLeft}}s</text>
      </view>
      <view class="info-item">
        <text class="info-label">提示</text>
        <text class="info-value">{{hints}}</text>
      </view>
    </view>
  </view>

  <!-- 单词显示区域 -->
  <view class="word-display-area">
    <view class="word-display">
      <view 
        wx:for="{{currentWordDisplay}}" 
        wx:key="index" 
        class="letter-slot {{item !== '_' ? 'filled' : ''}}"
      >
        {{item}}
      </view>
    </view>
    <view class="word-hint" wx:if="{{showHint}}">
      <text>提示：{{hintText}}</text>
    </view>
  </view>

  <!-- 键盘区域 -->
  <view class="keyboard-area">
    <view class="keyboard-title">选择字母</view>
    <view class="keyboard">
      <view 
        wx:for="{{availableLetters}}" 
        wx:key="index"
        class="letter-key"
        data-letter="{{item}}"
        data-index="{{index}}"
        bindtap="onLetterTap"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 游戏控制按钮 -->
  <view class="game-controls">
    <button 
      wx:if="{{gameStatus === 'ready'}}" 
      class="btn btn-primary start-btn"
      bindtap="startGame"
    >
      开始游戏
    </button>
    
    <view wx:if="{{gameStatus === 'playing'}}" class="playing-controls">
      <button class="btn btn-secondary" bindtap="pauseGame">暂停</button>
      <button class="btn btn-success" bindtap="useHint">使用提示</button>
      <button class="btn btn-danger" bindtap="skipWord">跳过单词</button>
    </view>
    
    <button 
      wx:if="{{gameStatus === 'finished'}}" 
      class="btn btn-primary"
      bindtap="initGame"
    >
      重新开始
    </button>
  </view>

  <!-- 游戏状态提示 -->
  <view class="game-status" wx:if="{{gameStatus === 'ready'}}">
    <text class="status-text">准备开始游戏！</text>
    <text class="status-desc">点击字母拼写单词，完成更多单词获得高分！</text>
  </view>

  <view class="game-status" wx:if="{{gameStatus === 'paused'}}">
    <text class="status-text">游戏已暂停</text>
    <button class="btn btn-primary" bindtap="resumeGame">继续游戏</button>
  </view>

  <!-- 已完成单词列表 -->
  <view class="completed-words" wx:if="{{completedWords.length > 0}}">
    <view class="completed-title">已完成单词 ({{completedWords.length}})</view>
    <view class="completed-list">
      <text 
        wx:for="{{completedWords}}" 
        wx:key="index"
        class="completed-word"
      >
        {{item}}
      </text>
    </view>
  </view>
</view>
