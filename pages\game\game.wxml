<!--pages/game/game.wxml-->
<view class="game-container">
  <!-- 页面加载成功提示 -->
  <view class="success-banner">
    <text class="success-title">🎉 游戏页面加载成功！</text>
    <text class="success-desc">页面跳转正常，游戏功能已就绪</text>
  </view>

  <!-- 游戏信息栏 -->
  <view class="game-info-bar">
    <view class="info-item">
      <text class="info-label">得分</text>
      <text class="info-value">{{score}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">等级</text>
      <text class="info-value">{{level}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">时间</text>
      <text class="info-value">{{timeLeft}}s</text>
    </view>
  </view>

  <!-- 当前单词显示 -->
  <view class="word-section">
    <text class="word-title">当前单词:</text>
    <view class="word-display">
      <text
        wx:for="{{currentWordDisplay}}"
        wx:key="index"
        class="letter-slot {{item !== '_' ? 'filled' : ''}}"
      >
        {{item}}
      </text>
    </view>
    <text class="word-hint">目标: {{currentWord}}</text>
  </view>

  <!-- 字母键盘 -->
  <view class="keyboard-section" wx:if="{{availableLetters.length > 0}}">
    <text class="keyboard-title">选择字母:</text>
    <view class="keyboard">
      <button
        wx:for="{{availableLetters}}"
        wx:key="index"
        class="letter-btn"
        data-letter="{{item}}"
        data-index="{{index}}"
        bindtap="onLetterTap"
      >
        {{item}}
      </button>
    </view>
  </view>

  <!-- 游戏控制 -->
  <view class="game-controls">
    <button
      wx:if="{{gameStatus === 'ready'}}"
      class="btn btn-primary control-btn"
      bindtap="startGame"
    >
      🎮 开始游戏
    </button>

    <view wx:if="{{gameStatus === 'playing'}}" class="playing-controls">
      <button class="btn btn-warning control-btn" bindtap="resetGame">
        🔄 重新开始
      </button>
      <button class="btn btn-secondary control-btn" bindtap="pauseGame">
        ⏸️ 暂停
      </button>
    </view>

    <button
      wx:if="{{gameStatus === 'paused'}}"
      class="btn btn-success control-btn"
      bindtap="resumeGame"
    >
      ▶️ 继续游戏
    </button>
  </view>

  <!-- 调试信息 -->
  <view class="debug-section">
    <text class="debug-title">🔧 调试信息:</text>
    <text class="debug-text">单词: {{currentWord || '未设置'}}</text>
    <text class="debug-text">状态: {{gameStatus}}</text>
    <text class="debug-text">输入: {{userInput}}</text>
    <text class="debug-text">字母数: {{availableLetters.length}}</text>
    <button class="btn btn-link debug-btn" bindtap="showDebugInfo">
      显示详细调试
    </button>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="btn btn-secondary back-btn" bindtap="goBack">
      ← 返回首页
    </button>
  </view>
</view>