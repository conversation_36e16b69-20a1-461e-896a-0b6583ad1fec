<!--pages/index/index.wxml-->
<view class="container">
  <!-- 游戏标题 -->
  <view class="game-title">
    <text class="title-main">背单词切字母</text>
    <text class="title-sub">Word Learning Game</text>
  </view>

  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <block wx:if="{{hasUserInfo}}">
      <view class="user-info">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
        <text class="user-name">{{userInfo.nickName}}</text>
      </view>
    </block>
    <block wx:else>
      <view class="user-info">
        <image class="user-avatar" src="/images/default-avatar.png" mode="cover"></image>
        <button 
          wx:if="{{canIUseGetUserProfile}}" 
          class="btn btn-primary get-user-btn"
          bindtap="getUserProfile"
        >
          获取头像昵称
        </button>
        <button 
          wx:elif="{{canIUse}}" 
          class="btn btn-primary get-user-btn"
          open-type="getUserInfo" 
          bindgetuserinfo="getUserInfo"
        >
          获取头像昵称
        </button>
        <text wx:else class="user-name">游客</text>
      </view>
    </block>
  </view>

  <!-- 游戏统计 -->
  <view class="game-stats">
    <view class="stats-title">游戏统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalGames}}</text>
        <text class="stat-label">总游戏次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.bestScore}}</text>
        <text class="stat-label">最高分数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalWords}}</text>
        <text class="stat-label">完成单词数</text>
      </view>
    </view>
  </view>

  <!-- 主要操作按钮 -->
  <view class="main-actions">
    <button class="btn btn-primary start-game-btn" bindtap="startGame">
      <text class="btn-icon">🎮</text>
      <text class="btn-text">开始游戏</text>
    </button>
    
    <view class="action-grid">
      <button class="btn btn-secondary action-btn" bindtap="showGameRules">
        <text class="btn-icon">📖</text>
        <text class="btn-text">游戏规则</text>
      </button>
      
      <button class="btn btn-secondary action-btn" bindtap="viewGameHistory">
        <text class="btn-icon">📊</text>
        <text class="btn-text">游戏记录</text>
      </button>
      
      <button class="btn btn-secondary action-btn" bindtap="openSettings">
        <text class="btn-icon">⚙️</text>
        <text class="btn-text">设置</text>
      </button>
      
      <button class="btn btn-secondary action-btn" open-type="share">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享游戏</text>
      </button>
    </view>
  </view>

  <!-- 游戏特色介绍 -->
  <view class="game-features">
    <view class="features-title">游戏特色</view>
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-icon">🎯</text>
        <text class="feature-text">趣味拼词，寓教于乐</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">⏰</text>
        <text class="feature-text">限时挑战，刺激有趣</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🏆</text>
        <text class="feature-text">等级系统，持续进步</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">💡</text>
        <text class="feature-text">智能提示，轻松学习</text>
      </view>
    </view>
  </view>
</view>
