<!--pages/index/index.wxml-->
<view class="container">
  <!-- 应用标题 -->
  <view class="app-header">
    <text class="app-title">🎮 背单词切字母</text>
    <text class="app-subtitle">Word Learning Game</text>
    <text class="app-version">v2.0 简化版</text>
  </view>

  <!-- 快速开始 -->
  <view class="quick-start">
    <button class="btn btn-primary start-game-btn" bindtap="startGame">
      🚀 开始游戏
    </button>
    <text class="start-hint">点击开始你的单词学习之旅</text>
  </view>

  <!-- 游戏统计 -->
  <view class="game-stats card">
    <text class="stats-title">📊 游戏统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalGames}}</text>
        <text class="stat-label">总游戏次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.bestScore}}</text>
        <text class="stat-label">最高分数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalWords}}</text>
        <text class="stat-label">完成单词数</text>
      </view>
    </view>
  </view>

  <!-- 功能按钮 -->
  <view class="function-buttons">
    <button class="btn btn-secondary" bindtap="openSettings">
      ⚙️ 设置
    </button>
    <button class="btn btn-secondary" bindtap="viewGameHistory">
      📈 游戏记录
    </button>
    <button class="btn btn-warning" bindtap="showGameRules">
      📖 游戏规则
    </button>
  </view>

  <!-- 调试信息 -->
  <view class="debug-info" wx:if="{{showDebug}}">
    <text class="debug-title">🔧 调试信息</text>
    <text class="debug-text">单词库数量: {{wordListCount}}</text>
    <text class="debug-text">应用版本: {{appVersion}}</text>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="footer-text">轻松学习，快乐记忆</text>
    <button class="btn-link" bindtap="toggleDebug">
      {{showDebug ? '隐藏' : '显示'}}调试信息
    </button>
  </view>
</view>

  <!-- 游戏统计 -->
  <view class="game-stats">
    <view class="stats-title">游戏统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalGames}}</text>
        <text class="stat-label">总游戏次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.bestScore}}</text>
        <text class="stat-label">最高分数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalWords}}</text>
        <text class="stat-label">完成单词数</text>
      </view>
    </view>
  </view>

  <!-- 主要操作按钮 -->
  <view class="main-actions">
    <button class="btn btn-primary start-game-btn" bindtap="startGame">
      <text class="btn-icon">🎮</text>
      <text class="btn-text">开始游戏</text>
    </button>
    
    <view class="action-grid">
      <button class="btn btn-secondary action-btn" bindtap="showGameRules">
        <text class="btn-icon">📖</text>
        <text class="btn-text">游戏规则</text>
      </button>
      
      <button class="btn btn-secondary action-btn" bindtap="viewGameHistory">
        <text class="btn-icon">📊</text>
        <text class="btn-text">游戏记录</text>
      </button>
      
      <button class="btn btn-secondary action-btn" bindtap="openSettings">
        <text class="btn-icon">⚙️</text>
        <text class="btn-text">设置</text>
      </button>
      
      <button class="btn btn-secondary action-btn" open-type="share">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享游戏</text>
      </button>

      <button class="btn btn-warning action-btn" bindtap="diagnoseNavigation">
        <text class="btn-icon">🔧</text>
        <text class="btn-text">导航诊断</text>
      </button>
    </view>
  </view>

  <!-- 游戏特色介绍 -->
  <view class="game-features">
    <view class="features-title">游戏特色</view>
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-icon">🎯</text>
        <text class="feature-text">趣味拼词，寓教于乐</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">⏰</text>
        <text class="feature-text">限时挑战，刺激有趣</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🏆</text>
        <text class="feature-text">等级系统，持续进步</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">💡</text>
        <text class="feature-text">智能提示，轻松学习</text>
      </view>
    </view>
  </view>
</view>
